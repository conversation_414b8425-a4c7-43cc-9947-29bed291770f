<!-- Invite <PERSON> -->
<style>
    .truncate-text {
        display: inline-block;
        max-width: 300px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: middle;
        cursor: pointer;
    }
</style>
<div class="d-flex justify-content-end gap-2 align-items-center mb-3">
    <button type="button" class="mx-3 btn btn-primary"
        data-toggle="modal"
        data-target="#mainInstructorInvitePopup"
        id="send-invite"
        onclick="inviteMainInstructor('{{ route('admin.marketplace-requirementsInvite') }}', '{{ encrypt_str($data->id) }}')">
        Main Instructor Invite
    </button>
    <button type="button" class="btn btn-primary"
        data-toggle="modal"
        data-target="#standByInvitePopup"
        id="send-invite"
        onclick="inviteStandbyInstructor('{{ route('admin.marketplace-requirementsInvite') }}', '{{ encrypt_str($data->id) }}')">
        Standby Instructor Invite
    </button>
</div>

{{-- Proposal History Table --}}
<div class="card">
    <div class="card-header">
        Proposal History
    </div>
    <div class="card-body p-0">
        @if($data->reviewApplicants->count() > 0)
            <div class="table-responsive">
                <table class="table table-striped table-bordered mb-0">
                    <thead class="thead-dark">
                        <tr>
                            <th>#</th>
                            <th>Educator Name</th>
                            <th>Educator Email</th>
                            <th>Requirement Start Date</th>
                            <th>Invite ID</th>
                            <th>Offer Description</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($data->reviewApplicants as $index => $proposal)
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td>{{ $proposal->user->first_name }} {{ $proposal->user->last_name }}</td>
                                <td>{{ $proposal->user->email }}</td>
                                <td>{{ $proposal->requirement->start_date }}</td>
                                <td>{{ $proposal->id }}</td>
                                <td title="{{ $proposal->offer_description }}">{{ $proposal->offer_description }}</td>
                                <td>
                                    <select name="status" id="status_{{ $proposal->id }}" class="form-control" data-inviteId="{{ $proposal->id }}" data-field="status" onchange="updateStatus('{{ route('admin.marketplace-updateAppliedRequestStatus') }}', '{{ $proposal->id }}', this.value)">
                                        <option value="pending" {{ $proposal->status == 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="accepted" {{ $proposal->status == 'accepted' ? 'selected' : '' }}>Accepted</option>
                                        <option value="rejected" {{ $proposal->status == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                        <option value="withdraw" {{ $proposal->status == 'withdraw' ? 'selected' : '' }}>Withdrawn</option>
                                    </select>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <p class="text-muted m-3">No proposals sent yet.</p>
        @endif
    </div>
</div>


<!-- Modal (only for sending invites) -->
<div class="modal" id="mainInstructorInvitePopup" tabindex="-1" aria-labelledby="mainInstructorInvitePopup" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <!-- Content loaded by AJAX -->
        </div>
    </div>
</div>

<!-- Modal (only for sending invites) -->
<div class="modal" id="standByInvitePopup" tabindex="-1" aria-labelledby="standByInvitePopup" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <!-- Content loaded by AJAX -->
        </div>
    </div>
</div>


<!-- Scripts will be moved to parent file -->