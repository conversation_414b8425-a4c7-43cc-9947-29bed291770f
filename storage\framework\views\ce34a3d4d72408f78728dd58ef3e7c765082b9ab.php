

<?php $__env->startSection('title'); ?> Testimonial List | Whizara <?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php $res = get_permission(session('Adminnewlogin')['type']); ?>
    <!-- MAIN SECTION START -->
    <main class="content">
        <div class="container-fluid p-0">
            <!-- BREADCRUMB START -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php if(isset($res['dashboard'])): ?>
                        <?php if(array_key_exists('dashboard', $res)): ?>
                            <?php if(in_array('add', json_decode($res['dashboard'], true))): ?>
                                <li class="breadcrumb-item"><a href="<?php echo e(url('admin-dashboard')); ?>"
                                        class="text-primary"><?php echo e(__('messages.dashboard')); ?></a></li>
                            <?php endif; ?>
                        <?php endif; ?>
                    <?php endif; ?>

                    <li class="breadcrumb-item active" aria-current="page">Manage Testimonial</li>

                </ol>
            </nav>
            <!-- BREADCRUMB END -->

            <!-- MONTHLY INVOICES SECTION START -->
            <div class="table-responsive filterdata">
                <table id="dataTable" class="table table-striped" style="width:100%">
                    <thead class="thead-dark">
                        <tr>
                            <th>Id</th>
                            <th>Image</th>
                            <th>Name</th>
                            <th>Designation</th>
                            <th>Testimonial</th>
                            <th>Created date</th>
                            <th><?php echo e(__('messages.action')); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if(!empty($testimonial) && $testimonial->count()): ?>
                            <?php $__currentLoopData = $testimonial; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($data->id); ?></td>
                                    <td><img src="<?php echo e(asset('public/testimonial/' . $data->image)); ?>" width="100px"></td>
                                    <td><?php echo e($data->name); ?></td>
                                    <td><?php echo e($data->designation); ?></td>
                                    <td><?php echo e($data->description); ?></td>
                                    <td><?php echo e(getAdminTimestamp($data->created_at)); ?></td>
                                    <td>
                                        <div class="w-100 d-flex justify-content-center align-items-center">
                                            <a href="<?php echo e(url('edit-testimonial/' . encrypt_str($data->id))); ?>">
                                                <button class="btn btn-rounded btn-block btn-xs btn-outline-secondary"><i class="fa fa-pencil" aria-hidden="true"></i></button>
                                            </a>
                                            &nbsp;
                                            <a class="delete_data_testimonial" href="#" data-id="<?php echo e(encrypt_str($data->id)); ?>">
                                                <button class="btn btn-rounded btn-block btn-xs btn-outline-danger"><i class="fa fa-trash" aria-hidden="true"></i></button>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="10">No data found.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            <!-- END -->

            <!-- EDIT PROFILE SECTION END -->
        </div>
    </main>
    <!-- MAIN SECTION END -->
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/testimonials/index.blade.php ENDPATH**/ ?>