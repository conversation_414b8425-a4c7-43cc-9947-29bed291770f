

<?php $__env->startSection('title', 'Add Testimonial'); ?>

<?php $__env->startSection('content'); ?>
<main class="content">
    <div class="container-fluid p-0">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="<?php echo e(url('admin-dashboard')); ?>" class="text-primary"><?php echo e(__('messages.dashboard')); ?></a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">Add Testimonial</li>
            </ol>
        </nav>
        <form method="post" action="<?php echo e(route('testimonial.store')); ?>" id="addtestimonial" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            <div class="row justify-content-center">
                <div class="col-lg-12 col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <div id="testimonialRows">
                                <?php $__empty_1 = true; $__currentLoopData = $testimonial; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $test): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <div class="testimonialRow border rounded p-3 mb-3 position-relative">
                                    <input type="hidden" name="id[]" value="<?php echo e($test->id); ?>">

                                    <div class="row">
                                        <div class="col-md-6 form-group">
                                            <label>Name</label>
                                            <input type="text" class="form-control" name="name[]" value="<?php echo e($test->name); ?>" required>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label>Designation</label>
                                            <input type="text" class="form-control" name="designation[]" value="<?php echo e($test->designation); ?>" required>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 form-group">
                                            <label>Image</label>
                                            <input type="file" class="form-control" name="image[]">
                                        </div>
                                        <div class="col-md-5 form-group">
                                            <label>Description</label>
                                            <textarea class="form-control" name="description[]" required><?php echo e($test->description); ?></textarea>
                                        </div>
                                    </div>

                                    <div class="action-btns mt-5 mr-5">
                                        <?php if($key == 0): ?>
                                        <button type="button" class="btn btn-success addRow">+</button>
                                        <?php else: ?>
                                        <button type="button" class="btn btn-danger removeRowDB" data-id="<?php echo e($test->id); ?>">-</button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <div class="testimonialRow border rounded p-3 mb-3 position-relative">
                                    <div class="row">
                                        <div class="col-md-6 form-group">
                                            <label>Name</label>
                                            <input type="text" class="form-control" name="name[]" placeholder="Enter name" required>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label>Designation</label>
                                            <input type="text" class="form-control" name="designation[]" placeholder="Enter designation" required>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 form-group">
                                            <label>Image</label>
                                            <input type="file" class="form-control" name="image[]">
                                        </div>
                                        <div class="col-md-5 form-group">
                                            <label>Description</label>
                                            <textarea class="form-control" name="description[]" placeholder="Enter description" required></textarea>
                                        </div>
                                    </div>
                                    <div class="action-btns mt-5 mr-4">
                                        <button type="button" class="btn btn-success addRow">+</button>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="col-md-12 d-flex justify-content-md-end justify-content-between mt-3">
                                <a href="<?php echo e(redirect()->getUrlGenerator()->previous()); ?>" class="btn btn-secondary mr-2"><?php echo e(__('messages.cancel')); ?></a>
                                <button type="submit" class="btn btn-primary"><?php echo e(__('messages.add')); ?></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</main>

<style>
    .testimonialRow {
        position: relative;
        padding-right: 60px;
    }
    .action-btns {
        position: absolute;
        top: 50%;
        right: 15px;
        transform: translateY(-50%);
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function() {
        $(document).on("click", ".addRow", function() {
            let newRow = $(".testimonialRow:first").clone();
            newRow.find("input[type=text], textarea").val("");
            newRow.find("input[type=file]").val("");
            newRow.find("input[type=hidden]").remove();
            newRow.find("img").remove(); 
            newRow.find(".addRow")
                .removeClass("btn-success addRow")
                .addClass("btn-danger removeRow")
                .text("-");
            $("#testimonialRows").append(newRow);
        });

        $(document).on("click", ".removeRow", function() {
            $(this).closest(".testimonialRow").remove();
        });

        $(document).on("click", ".removeRowDB", function() {
            if (!confirm("Are you sure you want to delete this testimonial?")) return;
            let row = $(this).closest(".testimonialRow");
            let id = $(this).data("id");

            $.ajax({
                url: "<?php echo e(url('testimonial-delete')); ?>", 
                type: "POST",
                data: {
                    id: id,
                    _token: "<?php echo e(csrf_token()); ?>"
                },
                success: function(res) {
                    if (res.success) {
                        row.remove();
                    }
                }
            });
        });
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/testimonials/add.blade.php ENDPATH**/ ?>