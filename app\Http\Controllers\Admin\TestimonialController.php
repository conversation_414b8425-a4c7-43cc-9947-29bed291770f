<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Testimonials;
use App\V2\Core\Helpers\ApiResponse;
use Exception;
use Illuminate\Http\Request;

class TestimonialController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // if(get_childpermission(get_permission(session('Adminnewlogin')['type']),'managetestimonial','view')!=true){
        //     return redirect("/no-permission");
        // }
        $testimonial = Testimonials::all();
        return view("admin.testimonials.index", compact("testimonial"));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    // ***************Get-All-Testimonial***************
    public function getAllTestimonial()
    {
        try {
            $testimonial = Testimonials::where('status', 1)->get();
            return ApiResponse::success(["status" => "success", "testimonials" => $testimonial]);
        } catch (Exception $e) {
            return ApiResponse::error("Failed to fetch testimonial: " . $e->getMessage(), 500);
        }
    }
    // ***************Get-All-Testimonial***************
}
