

<?php $__env->startSection('title'); ?> List Applications | Whizara <?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<?php $res=get_permission(session('Adminnewlogin')['type']); ?>
<!-- MAIN SECTION START -->
<main class="content">
    <div class="container-fluid p-0">
        <!-- BREADCRUMB START -->
        <nav aria-label="breadcrumb">
        <?php if(session('Adminnewlogin')['type']!==4): ?>
            <ol class="breadcrumb">
                <!-- <li class="breadcrumb-item"><a href="<?php echo e(url('admin-dashboard')); ?>" class="text-primary"><?php echo e(__('messages.dashboard')); ?></a></li> -->
             
                 <?php if(isset($res['manageapplication'])): ?>
                 <?php if(array_key_exists('manageapplication',$res)): ?>
                 <?php if(in_array('new',json_decode($res['manageapplication'] ,true))): ?>
                  <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('new-application-list')); ?>" class="<?php if (request()->segment(1) == 'new-application-list'){echo "text-primary";}?>">New</a></li>
                 <?php endif; ?> <?php endif; ?> <?php endif; ?>
                
                 <?php if(isset($res['manageapplication'])): ?>
                 <?php if(array_key_exists('manageapplication',$res)): ?>
                 <?php if(in_array('Under Review',json_decode($res['manageapplication'] ,true))): ?>
                <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('Under-Reveiw-application-list')); ?>" class="<?php if (request()->segment(1) == 'Under-Reveiw-application-list'){echo "text-primary";}?>">Under Review</a></li>
                 <?php endif; ?> <?php endif; ?> <?php endif; ?>
               
                 <?php if(isset($res['manageapplication'])): ?>
                 <?php if(array_key_exists('manageapplication',$res)): ?>
                 <?php if(in_array('Resubmit request',json_decode($res['manageapplication'] ,true))): ?>
                <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('Resubmit-request-application-list')); ?>" class="<?php if (request()->segment(1) == 'Resubmit-request-application-list'){echo "text-primary";}?>">Resubmit request</a></li>
                 <?php endif; ?> <?php endif; ?> <?php endif; ?>
               
                 <?php if(isset($res['manageapplication'])): ?>
                 <?php if(array_key_exists('manageapplication',$res)): ?>
                 <?php if(in_array('Pending Interview',json_decode($res['manageapplication'] ,true))): ?>
                <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('Pending-Interview-application-list')); ?>" class="<?php if (request()->segment(1) == 'Pending-Interview-application-list'){echo "text-primary";}?>">Pending Interview</a></li>
                 <?php endif; ?> <?php endif; ?> <?php endif; ?>
                
                 <?php if(isset($res['manageapplication'])): ?>
                 <?php if(array_key_exists('manageapplication',$res)): ?>
                 <?php if(in_array('Approved',json_decode($res['manageapplication'] ,true))): ?>
                <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('Approved-application-list')); ?>" class="<?php if (request()->segment(1) == 'Approved-application-list'){echo "text-primary";}?>">Approved</a></li>
                <?php endif; ?> <?php endif; ?> <?php endif; ?>


                 <?php if(isset($res['manageapplication'])): ?>
                 <?php if(array_key_exists('manageapplication',$res)): ?>
                 <?php if(in_array('Active',json_decode($res['manageapplication'] ,true))): ?>
                <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('Active-application-list')); ?>" class="<?php if (request()->segment(1) == 'Active-application-list'){echo "text-primary";}?>">Active</a></li>
                <?php endif; ?> <?php endif; ?> <?php endif; ?>

                 <?php if(isset($res['manageapplication'])): ?>
                 <?php if(array_key_exists('manageapplication',$res)): ?>
                 <?php if(in_array('Rejected',json_decode($res['manageapplication'] ,true))): ?>
                <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('Reject-application-list')); ?>" class="<?php if (request()->segment(1) == 'Reject-application-list'){echo "text-primary";}?>">Rejected</a></li>
                 <?php endif; ?> <?php endif; ?> <?php endif; ?>
               
                 <?php if(isset($res['manageapplication'])): ?>
                 <?php if(array_key_exists('manageapplication',$res)): ?>
                 <?php if(in_array('All',json_decode($res['manageapplication'] ,true))): ?>
                <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('All-application-list')); ?>" class="<?php if (request()->segment(1) == 'All-application-list'){echo "text-primary";}?>">All</a></li>
                <?php endif; ?> <?php endif; ?> <?php endif; ?>



                <?php if(isset($res['manageapplication'])): ?>
                 <?php if(array_key_exists('manageapplication',$res)): ?>
                 <?php if(in_array('Filter',json_decode($res['manageapplication'] ,true))): ?>
                <li class="breadcrumb-item " aria-current="page"><a href="javascript:void(0);" class="filter">Filter</a></li>
                <?php endif; ?> <?php endif; ?> <?php endif; ?>
              
            </ol>
            <?php endif; ?>
        </nav>

        
        <!-- BREADCRUMB END -->
        <li class="breadcrumb-item " aria-current="page" style="float:right;">
                    <form  action="<?php echo e(route('admin.manage-application.export')); ?>" method="GET" autocomplete="off">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="btn p-0" style="padding: 11px 17px;"><i class="fa fa-download px-2" aria-hidden="true"></i>Excel Download</button>
                    </form>

                </li>

        <div class="table-responsive filterdata">
            <table class="table table-striped admin-dataTable" style="width:100%">
                <thead class="thead-dark">
                    <tr>
                        <th style="display:none">Id</th>
                        <th>Status</th>
                        <th>Application Start Date</th>
                        <th>Date Applied</th>
                        <th>Review Deadline</th>
                     
                        <th>Name</th>
                        <th>Email</th>
                        <th>City</th>
                        <th>State</th>
                        <th>Online/In-person</th>
                        <th>Profile type</th>
                        <?php if(isset($res['manageapplication'])): ?>
                        <?php if(array_key_exists('manageapplication',$res)): ?>
                          <?php if(in_array('chat',json_decode($res['manageapplication'] ,true))): ?>
                        <th><?php echo e(__('Chat')); ?></th>
                        <?php endif; ?>
                         <?php endif; ?>
                         <?php endif; ?>
                        <th><?php echo e(__('messages.action')); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if(!empty($application) && $application->count()): ?>
                    <?php $__currentLoopData = $application; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                    <td scope="row" style="display:none"><?php echo e($data->id); ?></td>
                        <td >
                           <?php if($data->email_verify_status==1): ?>
                           <?php if($data->profile_status == null): ?>
                           Account created
                           <?php else: ?>
                            <?php if($data->start_date): ?>
                            <?php echo profilestatus($data->profile_status)?>
                            <?php else: ?>
                            <?php if($data->profile_status > 1): ?>
                            <?php if($data->profile_status == 13): ?>
                            Rejected
                            <?php else: ?>
                            <?php echo profilestatus($data->profile_status)?>
                            <?php endif; ?>
                            <?php else: ?>
                            Account created
                            <?php endif; ?>
                            <?php endif; ?>
                            <?php endif; ?>
                            <?php else: ?>
                            Account created
                            <?php endif; ?>
                            <br>
                        <?php if($data->email_verify_status==1): ?>
                                        <span class="btn btn-success">Verified</span>
                                      <?php else: ?>
                                      <span class="btn btn-danger">Not Verified</span>
                                      <?php endif; ?>
                    
                    </td>
                    <td><?php if($data->start_date): ?><?php echo e(date('d-F-Y',strtotime($data->start_date))); ?><?php endif; ?></td>
                        <td><?php if($data->submission_date): ?><?php echo e(date('d-F-Y',strtotime($data->submission_date))); ?><?php endif; ?></td>
                        <td><?php if($data->review_deadline): ?><?php echo e(date('d-F-Y',strtotime($data->review_deadline))); ?><?php endif; ?></td>
                      
                        <td> 
                        <a  href="<?php echo e(url('view-application/step1/'.encrypt_str($data->id))); ?>" data-id="<?php echo e(encrypt_str($data->id)); ?>"><?php echo e($data->first_name); ?> <?php echo e($data->last_name); ?></a>

                             <!-- <?php if($data->profile_status >1): ?>
                              <a  href="<?php echo e(url('view-application/step1/'.encrypt_str($data->id))); ?>" data-id="<?php echo e(encrypt_str($data->id)); ?>"><?php echo e($data->first_name); ?> <?php echo e($data->last_name); ?></a>
                              <?php else: ?>
                              <?php echo e($data->first_name); ?> <?php echo e($data->last_name); ?>

                              <?php endif; ?> -->
                        </td>
                        <td><?php echo e($data->email); ?></td>
                        <td><?php echo e($data->city); ?></td>
                        <td><?php echo e($data->state); ?></td>
                        <td><?php echo e($data->teach); ?></td>
                        <td>
                            <?php if($data->teacher_type==='yes'): ?>
                            Certified Teacher
                           <?php else: ?>
                           <?php echo e($data->teacher_type); ?>

                          
                           <?php endif; ?>
                    </td>
                    <?php if(isset($res['manageapplication'])): ?>
                <?php if(array_key_exists('manageapplication',$res)): ?>
                <?php if(in_array('chat',json_decode($res['manageapplication'] ,true))): ?>
                                <td><a href="<?php echo e(url('instructor-chat/'.encrypt($data->id))); ?>"><i class="fas fa-comment fa-lg"></i></a></td>
                                &nbsp;
                                <?php endif; ?>
                                <?php endif; ?>
                                <?php endif; ?>
                        <td>
                            <div class="w-100 d-flex justify-content-center align-items-center">
                            <?php if(isset($res['manageapplication'])): ?>
    <?php if(in_array('view',json_decode($res['manageapplication'] ,true))): ?>
    <?php if(in_array('view',json_decode($res['manageapplication'] ,true))): ?>
    <?php if($data->profile_status >1): ?>
                                <a  href="<?php echo e(url('view-application/step1/'.encrypt_str($data->id))); ?>" data-id="<?php echo e(encrypt_str($data->id)); ?>">
                                    <button class="btn btn-rounded btn-block btn-xs btn-outline-secondary">Start Review</button>
                                </a>
                                <?php endif; ?>
    <?php endif; ?>
    <?php endif; ?>
    <?php endif; ?>

                                &nbsp;
                                <!-- <a href="<?php echo e(url('edit-district/'.encrypt_str($data->id))); ?>">
                                    <button class="btn btn-rounded btn-block btn-xs btn-outline-secondary"><i class="fa fa-pencil-square-o" aria-hidden="true"></i></button>
                                </a> -->
                                &nbsp;
                                <?php if(isset($res['manageapplication'])): ?>
    <?php if(in_array('delete',json_decode($res['manageapplication'] ,true))): ?>
    <?php if(in_array('delete',json_decode($res['manageapplication'] ,true))): ?>

                                <a class="admin_delete" href="javascript:void;" data-id="<?php echo e(encrypt_str($data->id)); ?>">
                                    <button class="btn btn-rounded btn-block btn-xs btn-outline-danger"><i class="fa fa-trash" aria-hidden="true"></i></button>
                                </a>
                                &nbsp;
   

   

    <?php endif; ?>
    <?php endif; ?>
    <?php endif; ?>
                            </div>
                        </td>

                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                    <tr>
                        <td colspan="10">No data found.</td>
                    </tr>
                    <?php endif; ?>
                </tbody>
                 <tfoot style="display:none;">
                    <tr>
                    <th style="display:none">Id</th>
                    <th>Status</th>
                        <th>Date Applied</th>
                        <th>Application Start Date</th>
                        <th>Review Deadline</th>
                      
                        <th>Name</th>
                        <th>Email</th>
                        <th>City</th>
                        <th>State</th>
                        <th>Online/In-person</th>
                        <th>Profile type</th>
                        <?php if(isset($res['manageapplication'])): ?>
                        <?php if(array_key_exists('manageapplication',$res)): ?>
                          <?php if(in_array('chat',json_decode($res['manageapplication'] ,true))): ?>
                        <th><?php echo e(__('Chat')); ?></th>
                        <?php endif; ?>
                         <?php endif; ?>
                         <?php endif; ?>
                        <th></th>
                     
                    </tr>
                </tfoot>
            </table>
        </div>
        <!-- END -->

        <!-- EDIT PROFILE SECTION END -->
    </div>
</main>
<!-- MAIN SECTION END -->



<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/application/list.blade.php ENDPATH**/ ?>