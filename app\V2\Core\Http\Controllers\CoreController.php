<?php

namespace App\V2\Core\Http\Controllers;

use DB;
use Exception;
use App\Classes;
use App\StateModel;
use App\Models\k12ConnectionCategorizedData;

use App\Http\Controllers\Controller;
use App\OnboardingInstructor;
use App\V2\Core\Helpers\ApiResponse;

class CoreController extends Controller
{
    /**
     * Get category-based info from school_management_setting
     * requirement_for | position_type | certificate | language | profile_type | per_hour_range | grades | states | program_type | timezone
     */
    public function getCategoryBasedInfo($type)
    {
        try {
            if($type == 'grades') {
                $grades = Classes::all();
                return ApiResponse::success(
                    $grades,
                    ucfirst($type) . " retrieved successfully."
                );
            } elseif ($type == 'states') {
                $states = StateModel::where(["country_id" => "239"])->where("id", "!=", "60")->get();
                return ApiResponse::success(
                    $states,
                    ucfirst($type) . " retrieved successfully."
                );
            } elseif ($type == 'program_type') {
                $program_type = k12ConnectionCategorizedData::where('type', 'program_type')->get();
                return ApiResponse::success(
                    $program_type,
                    ucfirst($type) . " retrieved successfully."
                );
            } elseif ($type == 'timezone') {
                $timezone = k12ConnectionCategorizedData::where('type', 'timezone')->get();
                return ApiResponse::success(
                    $timezone,
                    ucfirst($type) . " retrieved successfully."
                );
            }
            // requirement_for | position_type | certificate | language | profile_type | per_hour_range
            $record = DB::table('school_management_setting')
                ->where('type', $type)
                ->first();

            if (!$record) {
                return ApiResponse::error(
                    ucfirst($type) . " not found.",
                    404
                );
            }

            $values = json_decode($record->value, true);

            if (json_last_error() !== JSON_ERROR_NONE) {

                return ApiResponse::error(
                    "Data for {$type} is corrupted or invalid.",
                    500
                );
            }

            return ApiResponse::success(
                $values,
                ucfirst($type) . " retrieved successfully."
            );
        } catch (QueryException $e) {
            // Return a 500 server error response with a user-friendly message
            return ApiResponse::error('A database error occurred while saving the budget.', 500, $e);
        } catch (Exception $e) {

            return ApiResponse::error(
                "Something went wrong while retrieving {$type}.",
                500,
                config('app.debug') ? [$e->getMessage()] : []
            );
        }
    }

    // ***************Featured-Educator***************
    public function getFeaturedEducator() {
        try {
            // Get educators with class counts and filter for those with maximum classes
            $educatorsWithClassCounts = OnboardingInstructor::with(['step1', 'step2.education', 'step2.teching', 'step3.subjects.subSubject.subjectArea', 'step5', 'step6'])->whereIn('user_status', ['Active'])->withCount(['classesAsMainInstructor as main_classes_count', 'classesAsSubInstructor as sub_classes_count'])->get()->map(function ($educator) {
                $educator->total_classes_count = $educator->main_classes_count + $educator->sub_classes_count;
                return $educator;
            });

            // Find the maximum class count
            $maxClassCount = $educatorsWithClassCounts->max('total_classes_count');

            // Filter educators who have the maximum class count (or at least some classes if no one has max)
            $featuredEducators = $educatorsWithClassCounts->filter(function ($educator) use ($maxClassCount) {
                return $educator->total_classes_count > 0 && $educator->total_classes_count >= ($maxClassCount * 0.8); // Top 80% of max classes
            });

            // If no educators with classes found, get top 10 by ID
            if ($featuredEducators->isEmpty()) {
                $featuredEducators = $educatorsWithClassCounts->sortByDesc('id')->take(10);
            } else {
                $featuredEducators = $featuredEducators->sortByDesc('total_classes_count')->take(10);
            }

            $educators = $featuredEducators->map(function ($educator) {
                $subjects = [];
                if ($educator->step3 && $educator->step3->subjects) {
                    foreach ($educator->step3->subjects as $subject) {
                        $subjectData = [
                            'subject_area_name' => v1SubjectAreaName($subject->subject),
                            'sub_subject_name' => v1SubjectName($subject->sub_subject),
                            'proficiency' => $subject->proficiency,
                        ];
                        $subjects[] = $subjectData;
                    }
                }

                return [
                    'id' => $educator->id,
                    'title' => $educator->step5 ? $educator->step5->profile_title : null,
                    'first_name' => $educator->first_name,
                    'last_name' => $educator->last_name,
                    'image' => $educator->image ? generateSignedUrl($educator->image) : null,
                    'subject' => $subjects,
                    'total_classes_taught' => $educator->total_classes_count,
                ];
            });
            return ApiResponse::success($educators, "Featured educators fetched successfully");
        } catch (Exception $e) {
            return ApiResponse::error("Failed to fetch featured educators: " . $e->getMessage(), 500);
        }
    }
    // ***************Featured-Educator***************
}
