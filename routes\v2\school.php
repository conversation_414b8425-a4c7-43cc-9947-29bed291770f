<?php

use App\Http\Controllers\Admin\TestimonialController;
use Illuminate\Support\Facades\Route;
use App\V2\School\Http\Controllers\{AuthController, CalculateBudgetController, RequirementsController};


#region Open Routes
Route::group([
    'prefix' => 'schools',
    'as' => 'school.',
], function () {
    Route::post('login', [AuthController::class, 'login'])->name('login');
});

#region Auth Required
Route::group([
    'prefix' => 'schools',
    'as' => 'school.',
    'middleware' => ['CheckSchoolSession', 'auth:platform_school'],
], function () {
    // User Profile
    Route::get('profile', [AuthController::class, 'profile'])->name('profile');

    #region Calculate budget
    Route::get('subject-budgets/{subjectId}', [CalculateBudgetController::class, 'bySubject'])->name('getAllBudgets');
    Route::get('all-budgets', [CalculateBudgetController::class, 'fetchAllSchoolBudget'])->name('fetchAllSchoolBudget');
    Route::group([
        'prefix' => 'calculate-budgets',
        'as' => 'calculate_budgets.',
    ], function () {
        Route::apiResource('', CalculateBudgetController::class);
        Route::get('{id}/duplicate', [CalculateBudgetController::class, 'duplicate_budget'])->name('duplicate');
    });
    #endregion

    #region Post Requirement
    Route::group([
        'prefix' => 'requirements',
        'as' => 'requirements.',
    ], function () {
        Route::apiResource('', RequirementsController::class);
        Route::get('{id}/requirement', [RequirementsController::class, 'getRequirementById'])->name('requirement');
        Route::get('{id}/educator', [RequirementsController::class, 'getEducatorById'])->name('educator');
        Route::get('utils', [RequirementsController::class, 'getPostUtilities'])->name('utils');
        Route::get('{id}/duplicate', [RequirementsController::class, 'duplicateRequirement'])->name('duplicate');
        Route::get('{id}/review-applicants', [RequirementsController::class, 'reviewApplicantList'])->name('review-applicants');
        Route::post('like-dislike-applicant', [RequirementsController::class, 'likeDislikeApplicant'])->name('like-dislike-applicant');
        Route::get('get-applicant', [RequirementsController::class, 'getEligibleApplicant'])->name('get-applicant');
        Route::post('invite-applicant', [RequirementsController::class, 'inviteApplicant'])->name('invite-applicant');
        Route::get('invite-history', [RequirementsController::class, 'inviteHistory'])->name('invite-history');
    });
    #endregion

    // Testimonial
    Route::get('testimonial', [TestimonialController::class, 'getAllTestimonial'])->name('testimonial');
});
Route::get('featured-educator', [RequirementsController::class, 'getFeaturedEducator'])->name('featured-educator');