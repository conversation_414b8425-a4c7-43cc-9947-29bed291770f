<?php

use Illuminate\Support\Facades\Route;
use App\V2\Core\Http\Controllers\CoreController;
use App\V2\Core\Http\Controllers\SubjectAndBudgetController;

#region Open Routes
Route::group([
    'prefix' => 'core',
    'as' => 'core.',
], function () {
    Route::get('subjects', [SubjectAndBudgetController::class, 'getAllSubjects'])->name('getAllSubjects');
    Route::get('dropdown/{type}', [CoreController::class, 'getCategoryBasedInfo'])->name('getCategoryBasedInfo');
    // Featured Educator
    Route::get('featured-educator', [CoreController::class, 'getFeaturedEducator'])->name('featured-educator');
});